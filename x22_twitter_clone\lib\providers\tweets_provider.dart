import 'package:flutter/foundation.dart';
import '../models/tweet.dart';
import '../services/api_service.dart';

class TweetsProvider with ChangeNotifier {
  List<Tweet> _tweets = [];
  bool _isLoading = false;
  bool _hasMore = true;
  String? _errorMessage;
  int _currentOffset = 0;
  static const int _limit = 20;

  List<Tweet> get tweets => _tweets;
  bool get isLoading => _isLoading;
  bool get hasMore => _hasMore;
  String? get errorMessage => _errorMessage;

  // Load initial tweets
  Future<void> loadTweets({bool refresh = false}) async {
    if (_isLoading) return;

    if (refresh) {
      _currentOffset = 0;
      _hasMore = true;
      _tweets.clear();
    }

    _isLoading = true;
    _errorMessage = null;
    notifyListeners();

    try {
      final newTweets = await ApiService.getTweets(
        limit: _limit,
        offset: _currentOffset,
      );

      if (refresh) {
        _tweets = newTweets;
      } else {
        _tweets.addAll(newTweets);
      }

      _currentOffset += newTweets.length;
      _hasMore = newTweets.length == _limit;

      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _errorMessage = 'Failed to load tweets: $e';
      _isLoading = false;
      notifyListeners();
    }
  }

  // Load more tweets (pagination)
  Future<void> loadMoreTweets() async {
    if (!_hasMore || _isLoading) return;
    await loadTweets();
  }

  // Create new tweet
  Future<bool> createTweet({
    required String content,
    String? imageUrl,
    int? replyToTweetId,
  }) async {
    _isLoading = true;
    _errorMessage = null;
    notifyListeners();

    try {
      final result = await ApiService.createTweet(
        content: content,
        imageUrl: imageUrl,
        replyToTweetId: replyToTweetId,
      );

      if (result['success']) {
        // Refresh tweets to show the new tweet
        await loadTweets(refresh: true);
        return true;
      } else {
        _errorMessage = result['message'];
        _isLoading = false;
        notifyListeners();
        return false;
      }
    } catch (e) {
      _errorMessage = 'Failed to create tweet: $e';
      _isLoading = false;
      notifyListeners();
      return false;
    }
  }

  // Like/Unlike tweet
  void toggleLike(int tweetId) {
    final tweetIndex = _tweets.indexWhere((tweet) => tweet.id == tweetId);
    if (tweetIndex != -1) {
      final tweet = _tweets[tweetIndex];
      final newLikesCount = tweet.isLiked ? tweet.likesCount - 1 : tweet.likesCount + 1;
      
      _tweets[tweetIndex] = tweet.copyWith(
        isLiked: !tweet.isLiked,
        likesCount: newLikesCount,
      );
      
      notifyListeners();
      
      // TODO: Make API call to like/unlike tweet
    }
  }

  // Retweet/Unretweet
  void toggleRetweet(int tweetId) {
    final tweetIndex = _tweets.indexWhere((tweet) => tweet.id == tweetId);
    if (tweetIndex != -1) {
      final tweet = _tweets[tweetIndex];
      final newRetweetsCount = tweet.isRetweeted ? tweet.retweetsCount - 1 : tweet.retweetsCount + 1;
      
      _tweets[tweetIndex] = tweet.copyWith(
        isRetweeted: !tweet.isRetweeted,
        retweetsCount: newRetweetsCount,
      );
      
      notifyListeners();
      
      // TODO: Make API call to retweet/unretweet
    }
  }

  // Get tweets by user
  Future<List<Tweet>> getUserTweets(int userId) async {
    try {
      return await ApiService.getTweets(userId: userId);
    } catch (e) {
      throw Exception('Failed to load user tweets: $e');
    }
  }

  // Clear error message
  void clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  // Refresh tweets
  Future<void> refresh() async {
    await loadTweets(refresh: true);
  }
}
